import time
import pytest
from selenium import webdriver
from selenium.common import TimeoutException
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC


class UserCreationPage :
    textbox_username_xpath = "//*[@id='basic_id']"
    textbox_password_xpath = "//*[@id='basic_id_key']"
    button_login_xpath = "/html/body/div/div/div[2]/div/div/div[2]/div[2]/div/div[1]/form/div[5]/div/div/div/button"
    button_task_based_xpath = "/html/body/div[2]/div/div[2]/div/div[2]/div[2]/div/div/div/div/div/div[7]/div/div/div/div/h4/div/div/div[1]/div/div/img"

    admin_view_button = '/html/body/div/section/aside/div/div[2]/div[2]/div[1]/ul/li[18]/span/a/i'
    user_form_button = '/html/body/div/section/aside/div/div[2]/div[2]/div[1]/ul/li[3]/span/a/i'
    create_user_button = '/html/body/div/section/section/main/div[1]/div/div/div[2]/div/div[2]/div/div[1]/div[1]/button[1]/i'
    full_name_field = '//*[@id="user_name"]'
    code_field = '//*[@id="user_code"]'
    designation = '//*[@id="user_designation"]'
    select_specific_role = '/html/body/div[2]/div/div[2]/div/div[2]/div[2]/div/form/div[4]/div[2]/div/div/div/div/div'
    select_carpenter = '/html/body/div[4]/div/div/div/div[2]/div[1]/div/div/div[2]/div'
    select_location_group = '/html/body/div[2]/div/div[2]/div/div[2]/div[2]/div/form/div[5]/div[2]/div/div/div/div/div'
    select_lg_bangalore = '/html/body/div[5]/div/div/div/div[2]/div[1]/div/div/div[1]/div'
    select_reporting_feild = '//*[@id="user_reporting_to"]'
    select_reporting_name_Abhi = '/html/body/div[6]/div/div/div/div[2]/div[1]/div/div/div[2]'
    enter_mobile_number = '//*[@id="user_mobile"]'
    enter_email_xpath = '//*[@id="user_email"]'
    select_service_type_xpath = "/html/body/div[2]/div/div[2]/div/div[2]/div[2]/div/form/div[9]/div[2]/div/div/div/div/div"
    select_installation_service_type_xpath = "/html/body/div[7]/div/div/div/div[2]/div[1]/div/div/div[3]/div"
    enter_password_xpath = '//*[@id="password"]'
    confirm_password_xpath = '//*[@id="user_confirm_pass"]'
    click_submit_button_xpath = '/html/body/div[2]/div/div[2]/div/div[2]/div[2]/div/form/div[15]/div/div/div/button/span'
    search_user_name_xpath = '/html/body/div/section/section/main/div[1]/div/div/div[3]/div[1]/div/div[1]/div/div/input'

    click_existing_user_xpath = '/html/body/div/section/section/main/div[1]/div/div/div[3]/div[2]/div/div[1]/div/div[2]/div/div/div/div/div/div[2]/div/p/span'
    update_existing_user_xpath = '/html/body/div/section/section/main/div[1]/div/div/div[3]/div[2]/div/div[1]/div/div[2]/div/div/div/div/div/div[2]/div/p/span'
    click_new_updated_user_xpath = '/html/body/div/section/section/main/div[1]/div/div/div[3]/div[2]/div/div[1]/div/div[2]/div/div/div/div/div/div[2]/div/div/span'


    active_inactive_toggle_button_xpath = '/html/body/div[2]/div/div[2]/div/div[2]/div[2]/div/form/div[12]/div[2]/div/div/button/div'
    active_title_xpath = "active_title_xpath = /html/body/div[2]/div/div[2]/div/div[2]/div[2]/div/form/div[13]/div/div[1]/p"




    def __init__(self,driver):
        self.driver = driver


    def setUserName(self,username):
        # self.driver.find_element(By.XPATH, self.textbox_username_xpath).clear()
        self.driver.find_element(By.XPATH, self.textbox_username_xpath).send_keys(username)


    def setPassword(self,password):
        # self.driver.find_element(By.XPATH, self.textbox_password_xpath).clear()
        self.driver.find_element(By.XPATH, self.textbox_password_xpath).send_keys(password)


    def clicklogin(self):
        # self.driver.find_element(By.XPATH, self.button_login_xpath).clear()
        self.driver.find_element(By.XPATH, self.button_login_xpath).click()


    def clickbrand(self):
        # self.driver.find_element(By.XPATH, self.button_service_provider_css_selector).clear()
        WebDriverWait(self.driver, 10).until(
            EC.presence_of_element_located((By.XPATH, self.button_task_based_xpath))
        ).click()


    def adminView(self):
        self.driver.find_element(By.XPATH, self.admin_view_button).click()


    def userButton(self):
        self.driver.find_element(By.XPATH, self.user_form_button).click()


    def creatUserbutton(self):
        self.driver.find_element(By.XPATH, self.create_user_button).click()


    def fullNamefield(self, fullname):
        self.driver.find_element(By.XPATH, self.full_name_field).clear()
        self.driver.find_element(By.XPATH, self.full_name_field).send_keys(fullname)


    def codeField(self, code):
        self.driver.find_element(By.XPATH, self.code_field).clear()
        self.driver.find_element(By.XPATH, self.code_field).send_keys(code)


    def designationFeild(self, designation):
        self.driver.find_element(By.XPATH, self.designation).clear()
        self.driver.find_element(By.XPATH, self.designation).send_keys(designation)


    def selectSpecificrole(self):
        self.driver.find_element(By.XPATH, self.select_specific_role).click()
        time.sleep(10)
        self.driver.find_element(By.XPATH, self.select_carpenter).click()
        time.sleep(10)
        self.driver.find_element(By.XPATH, self.select_specific_role).click()


    def selectLocationgroup(self):
        self.driver.find_element(By.XPATH, self.select_location_group).click()
        time.sleep(10)
        self.driver.find_element(By.XPATH, self.select_lg_bangalore).click()
        time.sleep(10)
        self.driver.find_element(By.XPATH, self.select_location_group).click()


    def selectReportingto(self):
        self.driver.find_element(By.XPATH, self.select_reporting_feild).click()
        time.sleep(10)
        self.driver.find_element(By.XPATH, self.select_reporting_name_Abhi).click()


    def enterMobilenumber(self, mobilenumber):
        self.driver.find_element(By.XPATH, self.enter_mobile_number).clear()
        self.driver.find_element(By.XPATH, self.enter_mobile_number).send_keys(mobilenumber)


    def enterEmail(self, emailuser):
        # self.driver.find_element(By.XPATH, self.enter_email_xpath).clear()
        self.driver.find_element(By.XPATH, self.enter_email_xpath).send_keys(emailuser)


    def serviceType(self):
        # self.driver.find_element(By.XPATH, self.select_service_type_xpath).clear()
        self.driver.find_element(By.XPATH, self.select_service_type_xpath).click()
        time.sleep(5)
        self.driver.find_element(By.XPATH, self.select_installation_service_type_xpath).click()
        self.driver.find_element(By.XPATH, self.select_service_type_xpath).click()


    def enterPassord(self, passwordofuser):
        # self.driver.find_element(By.XPATH, self.enter_password_xpath).clear()
        self.driver.find_element(By.XPATH, self.enter_password_xpath).click()
        self.driver.find_element(By.XPATH, self.enter_password_xpath).send_keys(passwordofuser)


    def confirmPassord(self, confirmpasswordofuser):
        # self.driver.find_element(By.XPATH, self.confirm_password_xpath).clear()
        self.driver.find_element(By.XPATH, self.confirm_password_xpath).click()
        self.driver.find_element(By.XPATH, self.confirm_password_xpath).send_keys(confirmpasswordofuser)


    def submitButton(self):
        self.driver.find_element(By.XPATH, self.click_submit_button_xpath).click()


    def searchUsername(self, fullname):
        self.driver.find_element(By.XPATH, self.search_user_name_xpath).clear()
        self.driver.find_element(By.XPATH, self.search_user_name_xpath).send_keys(fullname)


    def clickNewuser(self):
        self.driver.find_element(By.XPATH, self.click_existing_user_xpath).click()


    def updateExistinguser(self, updatefullname):
        self.driver.find_element(By.XPATH, self.click_existing_user_xpath).click()
        self.driver.find_element(By.XPATH, self.full_name_field).clear()
        self.driver.find_element(By.XPATH, self.full_name_field).send_keys(updatefullname)


    def clickUpdatedNewUser(self):
        self.driver.find_element(By.XPATH, self.click_new_updated_user_xpath).click()


    def clickExistingUserCheckActiveInactive(self,):
        self.driver.find_element(By.XPATH, self.click_existing_user_xpath).click()


    def activeTitle(self):
        self.driver.find_element (By.XPATH, self.active_title_xpath.title())



