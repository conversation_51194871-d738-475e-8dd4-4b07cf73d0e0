import time
import os
import datetime
from selenium.webdriver.common.by import By
from pageObject.LoginPage import LoginPage
from conftest import setup

class Test_001_Login:
    # Remove hardcoded URL and credentials
    username = os.environ["SIGNUP_USERNAME"]
    password = os.environ["PASSWORD"]

    # 🔹 Homepage Title Test
    def test_homepageTitle(self, setup):
        driver = setup
        try:
            base_url = os.environ["BASE_URL"]  # ✅ Use env variable
            driver.get(base_url)
            time.sleep(10)
            act_title = driver.title
            assert act_title == "TMS"
            print("\n" + "✅ Homepage Title Test : PASSED " + "\n")
        except Exception as e:
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
            screenshot_name = f"screenshot_homepage_title_error_{timestamp}.png"
            driver.save_screenshot(screenshot_name)
            print("\n" + "❌ Homepage Title Test : FAILED" + "\n")
            assert False

     # 🔹 Login Test
    def test_login(self, setup):
        driver = setup
        base_url = os.environ["BASE_URL"]  # ✅ Use inside method
        username = os.environ["SIGNUP_USERNAME"]  # ✅ Use inside method
        password = os.environ["PASSWORD"]

        driver.get(base_url)
        lp = LoginPage(driver)
        lp.setUserName(username)
        lp.setPassword(password)
        lp.clicklogin()
        time.sleep(10)
        lp.clickbrand()
        time.sleep(10)

        try:
            driver.find_element(By.XPATH, "/html/body/div/section/section/main/div[1]/div/div[1]/button/span[2]")
            print("\n" + "✅ Login TestCase : Passed" + "\n")
            print("\n" + "=" * 150 + "\n")
            assert True
        except:
            print("\n" + "❌ Login TestCase Failed" + "\n")
            print("\n" + "=" * 150 + "\n")
            assert False