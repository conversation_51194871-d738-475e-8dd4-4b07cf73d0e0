import time
import datetime
from selenium.webdriver.common.by import By
from pageObject.LoginPage import LoginPage
from conftest import setup  # Explicitly import the fixture

class Test_001_Login:
    baseURL = "https://tms.wify.co.in"
    username = "<EMAIL>"
    password = "test"

    # 🔹 Homepage Title Test
    def test_homepageTitle(self, setup):
        driver = setup
        try:
            driver.get(self.baseURL)
            time.sleep(10)  # Replace with explicit wait if possible
            act_title = driver.title
            assert act_title == "TMS"
            print("\n" + "✅ Homepage Title Test : PASSED " + "\n")

        except Exception as e:
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
            screenshot_name = f"screenshot_homepage_title_error_{timestamp}.png"
            driver.save_screenshot(screenshot_name)

            print("\n" + "❌ Homepage Title Test : FAILED" + "\n")
            # print(f"📸 Screenshot saved as {screenshot_name}")

            assert False  # Fail the test if an exception occurs

    # 🔹 Login Test
    def test_login(self, setup):
        driver = setup
        driver.get(self.baseURL)
        lp = LoginPage(driver)
        lp.setUserName(self.username)
        lp.setPassword(self.password)
        lp.clicklogin()
        time.sleep(10)
        lp.clickbrand()
        time.sleep(10)

        try:
            driver.find_element(By.XPATH, "/html/body/div/section/section/main/div[1]/div/div[1]/button/span[2]")
            print("\n" + "✅ Login TestCase : Passed" + "\n")
            print("\n" + "=" * 150 + "\n")
            assert True
        except:
            print("\n" + "❌ Login TestCase Failed" + "\n")
            print("\n" + "=" * 150 + "\n")
            assert False