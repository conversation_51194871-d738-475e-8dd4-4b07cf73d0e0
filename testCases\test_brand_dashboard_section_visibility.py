import time
import pytest
from selenium import webdriver
from selenium.common.exceptions import NoSuchElementException
from selenium.webdriver.common.by import By
from conftest import setup2

@pytest.mark.usefixtures("setup2")
class TestBrandDashboard:

    # 🔹 Service Request Section
    def test_service_request_section_visibility(self):
        time.sleep(3)
        self.dv.clickserviceRequest()
        print("Clicked on Service Request")
        time.sleep(3)
        try:
            self.driver.find_element(By.XPATH, "/html/body/div/section/section/main/div[1]/div/div[3]/div/div[2]/div/div[1]/h2/span")
            print("\n" + "✅ Service Request section is visible : PASSED " + "\n")
            assert True
        except NoSuchElementException:
            print("\n" + "❌ Service Request section is not visible : FAILED" + "\n")
            assert False

    # 🔹 Custom Fields Section
    def test_custom_fields_section_visibility(self):
        time.sleep(3)
        self.dv.clickCustomField()
        time.sleep(3)
        try:
            self.driver.find_element(By.XPATH, "/html/body/div/section/section/main/div[1]/div/div[3]/div/div[2]/div/div[2]/h2/span")
            print("\n" + "✅ Custom Fields section is visible : PASSED " + "\n")
            assert True
        except NoSuchElementException:
            print("\n" + "❌ Custom Fields section is not visible : FAILED" + "\n")
            assert False

    # 🔹 Ageing Report Section
    def test_ageing_report_section_visibility(self):
        self.dv.clickAgeingReport()
        time.sleep(3)
        try:
            self.driver.find_element(By.XPATH, "/html/body/div/section/section/main/div[1]/div/div[3]/div/div[2]/div/div[3]/h2/span")
            print("\n" + "✅ Ageing Report section is visible : PASSED " "\n")
            assert True
        except NoSuchElementException:
            print("\n" + "❌ Ageing Report section is not visible : FAILED" + "\n")
            assert False

    # 🔹 TAT Overview Section
    def test_tat_overview_section_visibility(self):
        self.dv.clickTatOverview()
        time.sleep(3)
        try:
            self.driver.find_element(By.XPATH, "/html/body/div/section/section/main/div[1]/div/div[3]/div/div[2]/div/div[4]/h2[1]/span")
            print("\n" + "✅ TAT Overview section is visible : PASSED " + "\n")
            assert True
        except NoSuchElementException:
            print("\n" + "❌ TAT Overview section is not visible : FAILED" + "\n")
            assert False

    # 🔹 Authority Wise Request Section
    def test_authority_wise_request_section_visibility(self):
        self.dv.clickAuthorityWiseRequest()
        time.sleep(3)
        try:
            self.driver.find_element(By.XPATH, "/html/body/div/section/section/main/div[1]/div/div[3]/div/div[1]/div[1]/div/div[5]/div")
            print("\n" + "✅ Authority Wise Request section is visible : PASSED " + "\n")
            assert True
        except NoSuchElementException:
            print("\n" + "❌ Authority Wise Request section is not visible : FAILED" + "\n")
            assert False

    # 🔹 Recent Request Section
    def test_recent_request_section_visibility(self):
        self.dv.clickRecentRequest()
        time.sleep(3)
        try:
            self.driver.find_element(By.XPATH, "/html/body/div/section/section/main/div[1]/div/div[3]/div/div[2]/div/div[6]/div/div/div/h2/span")
            print("\n" + "✅ Recent Request section is visible : PASSED " + "\n")
            assert True
        except NoSuchElementException:
            print("\n" + "❌ Recent Request section is not visible : FAILED" + "\n")
            assert False

    # 🔹 Customer Feedback Section
    def test_customer_feedback_section_visibility(self):
        self.dv.clickCustomerFeedback()
        time.sleep(3)
        try:
            self.driver.find_element(By.XPATH, "/html/body/div[1]/section/section/main/div[1]/div/div[3]/div/div[2]/div/div[7]/h2/span")
            print("\n" + "✅ Customer Feedback section is visible : PASSED " + "\n")
            assert True
        except NoSuchElementException:
            print("\n" + "❌ Customer Feedback section is not visible : FAILED" + "\n")
            assert False
        print("\n" + "=" * 150 + "\n")