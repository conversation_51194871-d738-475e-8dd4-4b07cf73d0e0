
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains



class DashboardVisibility:
    textbox_username_xpath = "//*[@id='basic_id']"
    textbox_password_xpath = "//*[@id='basic_id_key']"
    button_login_xpath = "//*[@id='basic']/div[5]/div/div/div/button"
    button_login_brand_xpath ="//*[@id='basic']/div[5]/div/div/div/button"
    button_task_based_xpath = "/html/body/div[2]/div/div[2]/div/div[2]/div[2]/div/div/div/div/div/div[5]/div/div/div/div/h4/div/div/div[1]"
    button_sp_xpath = "/html/body/div[2]/div/div[2]/div/div[2]/div[2]/div/div/div/div/div/div[7]/div/div/div/div/h4/div/div/div[1]"
    button_wify_xpath = "/html/body/div[2]/div/div[2]/div/div[2]/div[2]/div/div/div/div/div/div[5]/div/div/div/div/h4/div/div/div[3]/h4/span"
    button_brand_xpath = "//span[contains(text(), 'Brand - Test Automation')]"

    # Brand Dashboard XPaths
    status_group_xpath = '/html/body/div[1]/section/section/main/div[1]/div/div[4]/div/div[1]/div[1]/div/div[1]/div'
    service_request_xpath = '/html/body/div/section/section/main/div[1]/div/div[3]/div/div[1]/div[1]/div/div[1]/div'
    location_group_xpath = '/html/body/div[1]/section/section/main/div[1]/div/div[4]/div/div[1]/div[1]/div/div[3]'
    custom_field_xpath = '/html/body/div/section/section/main/div[1]/div/div[3]/div/div[1]/div[1]/div/div[2]/div'
    ageing_report_xpath = '/html/body/div/section/section/main/div[1]/div/div[3]/div/div[1]/div[1]/div/div[3]/div'
    tat_overview_xpath = '/html/body/div/section/section/main/div[1]/div/div[3]/div/div[1]/div[1]/div/div[4]/div'
    authority_wise_request_xpath = '/html/body/div/section/section/main/div[1]/div/div[3]/div/div[1]/div[1]/div/div[5]/div'
    recent_request_xpath = '/html/body/div/section/section/main/div[1]/div/div[3]/div/div[1]/div[1]/div/div[6]/div'
    customer_feedback_xpath = '/html/body/div[1]/section/section/main/div[1]/div/div[3]/div/div[1]/div[1]/div/div[7]'

    # Service Provider Dashboard XPaths
    SP_customer_request_xpath = '/html/body/div/section/section/main/div[1]/div/div[3]/div/div[1]/div[1]/div/div[1]/div'
    SP_provider_status_group_xpath = '/html/body/div/section/section/main/div[1]/div/div[3]/div/div[1]/div[1]/div/div[2]/div'
    SP_customer_service_statuses_xpath = '/html/body/div/section/section/main/div[1]/div/div[3]/div/div[1]/div[1]/div/div[3]/div'
    SP_recent_requests_xpath = '/html/body/div/section/section/main/div[1]/div/div[3]/div/div[1]/div[1]/div/div[4]/div'
    SP_pool_view_xpath = '/html/body/div/section/section/main/div[1]/div/div[3]/div/div[1]/div[1]/div/div[5]/div'
    SP_status_group_ageing_xpath = '/html/body/div/section/section/main/div[1]/div/div[3]/div/div[1]/div[1]/div/div[6]/div'
    SP_customer_ageing_xpath = '/html/body/div/section/section/main/div[1]/div/div[3]/div/div[1]/div[1]/div/div[7]/div'

    def __init__(self, driver):
        self.driver = driver


    def setUserName(self,username):
        # self.driver.find_element(By.XPATH, self.textbox_username_xpath).clear()
        self.driver.find_element(By.XPATH, self.textbox_username_xpath).send_keys(username)


    def setPassword(self,password):
        # self.driver.find_element(By.XPATH, self.textbox_password_xpath).clear()
        self.driver.find_element(By.XPATH, self.textbox_password_xpath).send_keys(password)

    def clicklogin(self):
        WebDriverWait(self.driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, self.button_login_xpath))
        ).click()

    def clicklogin(self):
        WebDriverWait(self.driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, self.button_login_xpath))
        ).click()

    def clickloginBrand(self):
        self.driver.find_element(By.XPATH, self.button_login_brand_xpath).click()


    def clickbrand(self):
        WebDriverWait(self.driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, self.button_brand_xpath))
        ).click()

    def clicksp(self):
        WebDriverWait(self.driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, self.button_sp_xpath))
        ).click()

    def clickStatusGroup(self):
        self.driver.find_element(By.XPATH, self.status_group_xpath).click()

    def clickserviceRequest(self):
        self.driver.find_element(By.XPATH, self.service_request_xpath).click()

    def clickLocationGroup(self):
        self.driver.find_element(By.XPATH, self.location_group_xpath).click()

    def clickCustomField(self):
        self.driver.find_element(By.XPATH, self.custom_field_xpath).click()

    def clickAgeingReport(self):
        self.driver.find_element(By.XPATH, self.ageing_report_xpath).click()

    def clickTatOverview(self):
        self.driver.find_element(By.XPATH, self.tat_overview_xpath).click()

    def clickRecentRequest(self):
        self.driver.find_element(By.XPATH, self.recent_request_xpath).click()

    def clickAuthorityWiseRequest(self):
        self.driver.find_element(By.XPATH, self.authority_wise_request_xpath).click()

    def clickCustomerFeedback(self):
        self.driver.find_element(By.XPATH, self.customer_feedback_xpath).click()

    # Service Provider Dashboard Methods
    def clickSPCustomerRequest(self):
        self.driver.find_element(By.XPATH, self.SP_customer_request_xpath).click()

    def clickSPStatusGroup(self):
        self.driver.find_element(By.XPATH, self.SP_provider_status_group_xpath).click()

    def clickSPCustomerServiceStatuses(self):
        self.driver.find_element(By.XPATH, self.SP_customer_service_statuses_xpath).click()

    def clickSPRecentRequests(self):
        self.driver.find_element(By.XPATH, self.SP_recent_requests_xpath).click()

    def clickSPPoolView(self):
        self.driver.find_element(By.XPATH, self.SP_pool_view_xpath).click()

    def clickSPStatusGroupAgeing(self):
        self.driver.find_element(By.XPATH, self.SP_status_group_ageing_xpath).click()

    def clickSPCustAgeing(self):
        self.driver.find_element(By.XPATH, self.SP_customer_ageing_xpath).click()
