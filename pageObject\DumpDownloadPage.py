import email
import time
import click as click
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC





class DumpDownloadPage:
    textbox_username_xpath = "//*[@id='basic_id']"
    textbox_password_xpath = "//*[@id='basic_id_key']"
    button_login_xpath = "/html/body/div/div/div[3]/div/div/div[2]/div[2]/div/div[1]/form/div[5]/div/div/div/button"
    button_task_based_xpath = "/html/body/div[2]/div/div[2]/div/div[2]/div[2]/div/div/div/div/div/div[7]/div/div/div/div/h4/div/div/div[1]/div/div/img"
    button_profile_logout_xpath = "/html/body/div/section/aside/div/div[2]/div[1]/div[1]/div/div[1]/span"
    link_logout_xpath = "/html/body/div[2]/div/div/div/div[2]/div/ul/li[3]"


    service_request_installation_xpath = "/html/body/div/section/aside/div/div[2]/div[2]/div[1]/ul/li[10]/span/a"
    three_dot_export_request_button_xpath = '/html/body/div[1]/section/section/main/div[1]/div/div/div[3]/div[1]/div/div[2]/div/span/i'
    export_request_button_xpath = '/html/body/div[2]/div/div/ul/li/span/span'
    enter_email_xpath = '//*[@id="email_id"]'
    select_column_customer_info = '/html/body/div[3]/div/div[2]/div/div[2]/div/div/form/div[3]/div[1]/div[1]'
    select_column_mobno_cust_info = '/html/body/div[3]/div/div[2]/div/div[2]/div/div/form/div[3]/div[1]/div[2]/div/div/div/div/div/div/label[1]/span[1]/input'
    select_send_report_button_xpath = '/html/body/div[3]/div/div[2]/div/div[2]/div/div/form/div[4]/div/div/div/button/span[2]'




    def __init__(self,driver):
        self.driver = driver

    def setUserName(self,username):
        # self.driver.find_element(By.XPATH, self.textbox_username_xpath).clear()
        self.driver.find_element(By.XPATH, self.textbox_username_xpath).send_keys(username)


    def setPassword(self,password):
        # self.driver.find_element(By.XPATH, self.textbox_password_xpath).clear()
        self.driver.find_element(By.XPATH, self.textbox_password_xpath).send_keys(password)

    def clicklogin(self):
        # self.driver.find_element(By.XPATH, self.button_login_xpath).clear()
        self.driver.find_element(By.XPATH, self.button_login_xpath).click()


    def clickbrand(self):
        # self.driver.find_element(By.XPATH, self.button_service_provider_css_selector).clear()
        WebDriverWait(self.driver, 10).until(
            EC.presence_of_element_located((By.XPATH, self.button_task_based_xpath))
        ).click()

    def clicProfilebutton(self):
        # self.driver.find_element(By.XPATH, self.button_profile_logout_xpath).clear()
        self.driver.find_element(By.XPATH, self.button_profile_logout_xpath).click()


    def clickLogout(self):
        self.driver.find_element(By.XPATH, self.link_logout_xpath).click()


    def serviceRequestinstallation(self):
        self.driver.find_element(By.XPATH, self.service_request_installation_xpath).click()


    def threedotexportRequest(self):
        self.driver.find_element(By.XPATH, self.three_dot_export_request_button_xpath).click()


    def exportRequest(self):
        self.driver.find_element(By.XPATH, self.export_request_button_xpath).click()


    def enterEmail(self, exportemail):
        self.driver.find_element(By.XPATH, self.enter_email_xpath).clear()
        self.driver.find_element(By.XPATH, self.enter_email_xpath).click()
        self.driver.find_element(By.XPATH, self.enter_email_xpath).send_keys(exportemail)


    def selectColumncustomerinfo(self):
        self.driver.find_element(By.XPATH, self.select_column_customer_info).click()


    def selectMobnocolumncustinfo(self):
        self.driver.find_element(By.XPATH, self.select_column_mobno_cust_info).click()


    def selectSendreport(self):
        self.driver.find_element(By.XPATH, self.select_send_report_button_xpath).click()




