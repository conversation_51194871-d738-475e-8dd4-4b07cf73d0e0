import time

from selenium.webdriver import Keys
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

class LoginPage:
    def __init__(self, driver):
        self.driver = driver

    # Element locators
    textbox_username_xpath = "//*[@id='basic_id']"
    textbox_password_xpath = "//*[@id='basic_id_key']"
    button_login_xpath = "//*[@id='basic']/div[5]/div/div/div/button"
    button_brand_xpath = "//span[contains(text(), 'Brand - Test Automation')]"
    button_SP_wify_xpath = "/html/body/div[2]/div/div[2]/div/div[2]/div[2]/div/div/div/div/div/div[10]/div/div/div/div/h4/div/div/div[3]/h4/span"
    button_profile_logout_xpath = "/html/body/div[1]/section/aside/div/div[2]/div[1]/div[1]/div/div[2]/div/div[1]/span"
    link_logout_xpath = "/html/body/div[2]/div/div/div/div[2]/div/ul/li[3]"
    dashboard_shrikant_title_xpath = '/html/body/div/section/section/main/div[1]/div/div[2]/div/div/div/div/div/div[1]/div/h2'

    # Methods to interact with the login page
    def setUserName(self, username):
        """Enter username after waiting for the field to be visible."""
        WebDriverWait(self.driver, 10).until(
            EC.presence_of_element_located((By.XPATH, self.textbox_username_xpath))
        ).send_keys(username)

    def setPassword(self, password):
        password_field = WebDriverWait(self.driver, 15).until(
            EC.element_to_be_clickable((By.XPATH, self.textbox_password_xpath))
        )
        password_field.click()  # Ensure focus
        password_field.send_keys(password)  # Enter password
        time.sleep(2)  # Optional delay to verify

    def clicklogin(self):
        """Click the login button after waiting for it to be clickable."""
        WebDriverWait(self.driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, self.button_login_xpath))
        ).click()

    def clickbrand(self):
        """Click on the brand selection button after login."""
        WebDriverWait(self.driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, self.button_brand_xpath))
        ).click()

    def clickspwify(self):
        """Click on the SP Wify selection button."""
        WebDriverWait(self.driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, self.button_SP_wify_xpath))
        ).click()

    def clicProfilebutton(self):
        """Click the profile button for logout."""
        WebDriverWait(self.driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, self.button_profile_logout_xpath))
        ).click()

    def clickLogout(self):
        """Click the logout link."""
        WebDriverWait(self.driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, self.link_logout_xpath))
        ).click()

    def getDashboardTitle(self):
        """Get the dashboard title text."""
        return WebDriverWait(self.driver, 10).until(
            EC.presence_of_element_located((By.XPATH, self.dashboard_shrikant_title_xpath))
        ).text
