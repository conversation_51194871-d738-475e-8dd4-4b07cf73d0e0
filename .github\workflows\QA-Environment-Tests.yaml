name: Run QA Environment Tests and Send Slack Report

on:
  workflow_dispatch:
    inputs:
      qa_environment:
        description: "Select QA Environment"
        required: true
        default: "QA1"
        type: choice
        options:
          - QA1
          - QA2
          - QA3
          - QA4
          - QA5
      branch:
        description: "Branch to test (optional)"
        required: false
        default: "main"
        type: string

jobs:
  run-tests-and-send-to-slack:
    name: Run Tests on ${{ github.event.inputs.qa_environment }}
    runs-on: ubuntu-latest

    env:
      QA_ENVIRONMENT: ${{ github.event.inputs.qa_environment }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          ref: ${{ github.event.inputs.branch }}

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install selenium==4.* pytest pytest-html pytest-cov pytest-mock python-dotenv

      - name: Set environment parameter for pytest
        run: |
          case "${{ github.event.inputs.qa_environment }}" in
            "QA1")
              echo "PYTEST_ENV=qa01" >> $GITHUB_ENV
              ;;
            "QA2")
              echo "PYTEST_ENV=qa02" >> $GITHUB_ENV
              ;;
            "QA3")
              echo "PYTEST_ENV=qa03" >> $GITHUB_ENV
              ;;
            "QA4")
              echo "PYTEST_ENV=qa04" >> $GITHUB_ENV
              ;;
            "QA5")
              echo "PYTEST_ENV=qa05" >> $GITHUB_ENV
              ;;
          esac

      - name: Display selected environment
        run: |
          echo "Running tests on: ${{ github.event.inputs.qa_environment }}"
          echo "Pytest environment parameter: $PYTEST_ENV"
          echo "Branch: ${{ github.event.inputs.branch }}"

      - name: Run all tests and generate HTML report
        run: |
          pytest --maxfail=5 --disable-warnings --capture=no --html=report.html --self-contained-html
        env:
          TEST_ENVIRONMENT: ${{ github.event.inputs.qa_environment }}
        # continue-on-error: true # Let it proceed even if tests fail

      - name: Upload report as artifact
        uses: actions/upload-artifact@v4
        with:
          name: test-report-${{ github.event.inputs.qa_environment }}
          path: report.html
        if: always() # upload even if tests fail

  send_slack_report:
    name: Send Slack Message
    runs-on: ubuntu-latest
    needs: run-tests-and-send-to-slack # Ensure this job runs after the tests

    steps:
      - name: Send Beautiful Slack Notification with Report Link
        env:
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
          SLACK_CHANNEL_ID: "C08LVALJC6M" # Replace with your Slack Channel ID
          GITHUB_REPO: ${{ github.repository }}
          GITHUB_RUN_ID: ${{ github.run_id }}
          QA_ENV: ${{ github.event.inputs.qa_environment }}
          BRANCH_NAME: ${{ github.event.inputs.branch }}
        run: |
          GITHUB_RUN_URL="https://github.com/$GITHUB_REPO/actions/runs/$GITHUB_RUN_ID"

          MESSAGE="\
          ########################################\n\
          📢 *$QA_ENV Test Report Available*  \n\
          ########################################\n\n\
          🏷️ *Environment:* \`$QA_ENV\`  \n\
          🔀 *Branch:* \`$BRANCH_NAME\`  \n\
          🔗 *Report Link:* <$GITHUB_RUN_URL|Click to View>  \n\n\
          🚀 _Automated testing via DevOps Team_  \n"

          curl -X POST https://slack.com/api/chat.postMessage \
            -H "Authorization: Bearer $SLACK_BOT_TOKEN" \
            -H "Content-Type: application/json" \
            --data "$(jq -n \
              --arg channel "$SLACK_CHANNEL_ID" \
              --arg text "$MESSAGE" \
              '{channel: $channel, text: $text}')"
