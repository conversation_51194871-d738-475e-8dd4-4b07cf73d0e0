name: Run Lambda Selenium Test and Send Slack Report

# on:
#   workflow_dispatch:

on:
  workflow_dispatch:
  repository_dispatch:
    types: [trigger-repo-b-action]

jobs:
  run-tests-and-send-to-slack:
    name: Run Tests and Send Slack Report
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install selenium==4.* pytest pytest-html pytest-cov pytest-mock python-dotenv

      - name: Run all tests and generate HTML report
        run: |
          pytest --maxfail=5 --disable-warnings --capture=no --html=report.html --self-contained-html
        # continue-on-error: true # Let it proceed even if tests fail

      - name: Upload report as artifact
        uses: actions/upload-artifact@v4
        with:
          name: test-report
          path: report.html
        if: always() # upload even if tests fail

  send_slack_report:
    name: Send Slack Message
    runs-on: ubuntu-latest
    needs: run-tests-and-send-to-slack # Ensure this job runs after the tests

    steps:
      - name: Send Beautiful Slack Notification with Report Link
        env:
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
          SLACK_CHANNEL_ID: "C08LVALJC6M" # Replace with your Slack Channel ID
          GITHUB_REPO: ${{ github.repository }}
          GITHUB_RUN_ID: ${{ github.run_id }}
          GITHUB_REF_NAME: ${{ github.event.client_payload.branch }} # Access branch from Repo A's dispatch payload
        run: |
          GITHUB_RUN_URL="https://github.com/$GITHUB_REPO/actions/runs/$GITHUB_RUN_ID"

          MESSAGE="\
          ########################################\n\
          📢 *Dependency Check Report Available*  \n\
          ########################################\n\n\
          🔀 *Branch:* \`$GITHUB_REF_NAME\`  \n\
          🔗 *Report Link:* <$GITHUB_RUN_URL|Click to View>  \n\n\
          🚀 _Scanned and reported via DevOps Team_  \n"

          curl -X POST https://slack.com/api/chat.postMessage \
            -H "Authorization: Bearer $SLACK_BOT_TOKEN" \
            -H "Content-Type: application/json" \
            --data "$(jq -n \
              --arg channel "$SLACK_CHANNEL_ID" \
              --arg text "$MESSAGE" \
              '{channel: $channel, text: $text}')"
