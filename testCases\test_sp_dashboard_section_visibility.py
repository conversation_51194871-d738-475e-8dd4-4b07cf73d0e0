import time
import pytest
from selenium import webdriver
from selenium.common.exceptions import NoSuchElementException
from selenium.webdriver.common.by import By
from conftest import setup1  # Temporary fix if pytest isn't detecting it


@pytest.mark.usefixtures("setup1")
class TestSPDashboard:

    # 🔹 Service Request Section
    def test_customer_request_section_visibility(self):
        # self.dv.clickSPCustomerRequest()
        time.sleep(3)
        try:
            self.driver.find_element(By.XPATH,
                                     "/html/body/div/section/section/main/div[1]/div/div[3]/div/div[1]/div[1]/div/div[1]/div")
            print("\n" + "✅ Customer Request section is visible : PASSED " + "\n")
            assert True
        except NoSuchElementException:
            print("\n" + "❌ Customer Request section is not visible : FAILED" + "\n")
            assert False

    # 🔹 Status Group Section
    # def test_status_group_section_visibility(self):
    #     # self.dv.clickSPStatusGroup()
    #     time.sleep(3)
    #     try:
    #         self.driver.find_element(By.XPATH,
    #                                  "/html/body/div/section/section/main/div[1]/div/div[3]/div/div[2]/div/div[2]/h2")
    #         print("\n" + "✅ Status Group section is visible : PASSED " + "\n")
    #         assert True
    #     except NoSuchElementException:
    #         print("\n" + "❌ Status Group section is not visible : FAILED" + "\n")
    #         assert False

    # 🔹 Recent Requests Section
    # def test_recent_requests_section_visibility(self):
    #     # self.dv.clickSPRecentRequests()
    #     time.sleep(3)
    #     try:
    #         self.driver.find_element(By.XPATH,
    #                                  "/html/body/div/section/section/main/div[1]/div/div[3]/div/div[2]/div/div[4]/div/div/h2")
    #         print("\n" + "✅ Recent Requests section is visible : PASSED " + "\n")
    #         assert True
    #     except NoSuchElementException:
    #         print("\n" + "❌ Recent Requests section is not visible : FAILED" + "\n")
    #         assert False

    # 🔹 Customer Service Statuses Section
    # def test_customer_service_statuses_section_visibility(self):
    #     # self.dv.clickSPCustomerServiceStatuses()
    #     time.sleep(3)
    #     try:
    #         self.driver.find_element(By.XPATH,
    #                                  "/html/body/div/section/section/main/div[1]/div/div[3]/div/div[2]/div/div[3]/h2")
    #         print("\n" + "✅ Customer Service Statuses section is visible : PASSED " + "\n")
    #         assert True
    #     except NoSuchElementException:
    #         print("\n" + "❌ Customer Service Statuses section is not visible : FAILED" "\n")
    #         assert False

    # 🔹 Pool View Section
    def test_pool_view_section_visibility(self):
        # self.dv.clickSPPoolView()
        time.sleep(3)
        try:
            self.driver.find_element(By.XPATH,
                                     "/html/body/div/section/section/main/div[1]/div/div[3]/div/div[1]/div[1]/div/div[5]/div")
            print("\n" + "✅ Pool View section is visible : PASSED " + "\n")
            assert True
        except NoSuchElementException:
            print("\n" + "❌ Pool View section is not visible : FAILED" + "\n")
            assert False
        print("\n" + "=" * 150 + "\n")