import time
from pageObject.LoginPage import LoginPage
import os
import datetime
from conftest import setup3  # Explicitly import the fixture

class Test_001_Logout:

    def test_logout(self, setup3):
        driver = setup3

        # ✅ Use values from environment AFTER pytest has set them
        baseURL = os.environ["BASE_URL"]
        username = os.environ["SIGNUP_USERNAME"]
        password = os.environ["PASSWORD"]

        driver.get(baseURL)
        lp = LoginPage(driver)

        try:
            lp.setUserName(username)
            lp.setPassword(password)
            lp.clicklogin()

            time.sleep(5)
            lp.clickbrand()

            time.sleep(5)
            lp.clicProfilebutton()

            time.sleep(5)
            lp.clickLogout()
            time.sleep(5)

            # Validate title after logout
            act_title = driver.title
            assert act_title == "TMS"

            print("\n" + "✅ Logout TestCase Passed : PASSED " + "\n")
            print("\n" + "=" * 150 + "\n")

        except Exception as e:
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
            screenshot_name = f"screenshot_logout_error_{timestamp}.png"
            driver.save_screenshot(screenshot_name)

            print("\n" + "❌ Logout TestCase Failed : FAILED" + "\n")
            # print(f"Screenshot saved as {screenshot_name}")
            print("\n" + "=" * 150 + "\n")
            assert False  # Fail the test if any exception occurs
