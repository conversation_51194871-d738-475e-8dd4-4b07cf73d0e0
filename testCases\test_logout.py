import time
from pageObject.LoginPage import LoginPage
import datetime
from conftest import setup3  # Explicitly import the fixture

class Test_001_Logout:
    baseURL = "https://tms.wify.co.in"
    username = "<EMAIL>"
    password = "test"

    def test_logout(self, setup3):
        driver = setup3
        driver.get(self.baseURL)
        lp = LoginPage(driver)

        try:
            lp.setUserName(self.username)
            lp.setPassword(self.password)
            lp.clicklogin()

            time.sleep(10)  # Replace with explicit wait
            lp.clickbrand()

            time.sleep(10)
            lp.clicProfilebutton()

            time.sleep(10)
            lp.clickLogout()

            # Validate title after logout
            act_title = driver.title
            assert act_title == "TMS"

            print("\n" + "✅ Logout TestCase Passed : PASSED " + "\n")
            print("\n" + "=" * 150 + "\n")

        except Exception as e:
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
            screenshot_name = f"screenshot_logout_error_{timestamp}.png"
            driver.save_screenshot(screenshot_name)

            print("\n" + "❌ Logout TestCase Failed : FAILED" + "\n")
            # print(f"Screenshot saved as {screenshot_name}")
            print("\n" + "=" * 150 + "\n")
            assert False  # Fail the test if any exception occurs
