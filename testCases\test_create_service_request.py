import time
import pytest
from conftest import setup4
from pageObject.LoginPage import LoginPage
from pageObject.ServiceRequestCreationPage import ServiceRequestCreationPage
from selenium.common.exceptions import NoSuchElementException, TimeoutException, ElementClickInterceptedException

@pytest.mark.usefixtures("setup4")
class TestCreateServiceRequest:
    baseURL = "https://tms.wify.co.in"
    username = "<EMAIL>"
    password = "test"

    def test_create_service_request(self, setup4):
        driver = setup4
        driver.get(self.baseURL)
        lp = LoginPage(driver)
        srp = ServiceRequestCreationPage(driver)

        try:
            # Perform login
            lp.setUserName(self.username)
            lp.setPassword(self.password)
            lp.clicklogin()
            time.sleep(10)
            lp.clickbrand()
            time.sleep(10)

            # Create Service Request
            srp.navigate_to_task_based()
            srp.click_add_button()
            srp.enter_customer_mobile("8855802429")
            srp.enter_customer_name("Tufel")
            srp.enter_request_description("Automation Testing 103")
            srp.select_priority_high()
            srp.click_submit_button()

            print("\n✅ Create Service Request: Passed\n")
            print("\n" + "=" * 150 + "\n")

        except (NoSuchElementException, TimeoutException, ElementClickInterceptedException) as e:
            print(f"\n❌ Create Service Request Failed: {str(e)}\n")
            print("\n" + "=" * 150 + "\n")
            assert False  # Mark the test as failed