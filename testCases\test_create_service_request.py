import time
import os
import pytest
import random
from conftest import setup4
from pageObject.LoginPage import LoginPage
from pageObject.ServiceRequestCreationPage import ServiceRequestCreationPage
from selenium.common.exceptions import NoSuchElementException, TimeoutException, ElementClickInterceptedException

@pytest.mark.usefixtures("setup4")
class TestCreateServiceRequest:

    def test_create_service_request(self, setup4):
        driver = setup4

        # ✅ Use values from environment
        base_url = os.environ["BASE_URL"]
        username = os.environ["SIGNUP_USERNAME"]
        password = os.environ["PASSWORD"]

        # ✅ Generate a random number between 100 and 999
        random_suffix = random.randint(100, 999)
        request_description = f"Automation Testing {random_suffix}"

        lp = LoginPage(driver)
        srp = ServiceRequestCreationPage(driver)

        try:
            # Perform login
            driver.get(base_url)
            lp.setUserName(username)
            lp.setPassword(password)
            lp.clicklogin()
            time.sleep(10)
            lp.clickbrand()
            time.sleep(10)

            # Create Service Request
            srp.navigate_to_task_based()
            srp.click_add_button()
            srp.enter_customer_mobile("8855802429")
            srp.enter_customer_name("Tufel")
            srp.enter_request_description(request_description)
            srp.select_priority_high()
            srp.click_submit_button()

            print("\n✅ Create Service Request: Passed\n")
            print("\n" + "=" * 150 + "\n")

        except (NoSuchElementException, TimeoutException, ElementClickInterceptedException) as e:
            print(f"\n❌ Create Service Request Failed: {str(e)}\n")
            print("\n" + "=" * 150 + "\n")
            assert False
