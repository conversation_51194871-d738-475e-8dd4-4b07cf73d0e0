import os
import time
import pytest
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from dotenv import load_dotenv
from pageObject.DashboardPage import DashboardVisibility

# ✅ Load .env file with override
load_dotenv(override=True)

# ✅ LambdaTest Credentials
LT_USERNAME = os.getenv("LT_USERNAME")
LT_ACCESS_KEY = os.getenv("LT_ACCESS_KEY")
LT_URL = f"https://{LT_USERNAME}:{LT_ACCESS_KEY}@hub.lambdatest.com/wd/hub"

# ✅ Add --env and --mode arguments
def pytest_addoption(parser):
    parser.addoption(
        "--env", action="store", default="prod", help="Environment: dev, demo, prod, qa01–qa05"
    )
    parser.addoption(
        "--mode", action="store", default="local", help="Run mode: local or lt (LambdaTest)"
    )

# ✅ Set BASE_URL and credentials from .env
@pytest.fixture(scope="session", autouse=True)
def set_environment(request):
    env = request.config.getoption("--env").lower()
    print(f"🌐 Setting up for {env.upper()} environment...")

    url_key = f"{env.upper()}_URL"
    base_url = os.getenv(url_key)

    if not base_url:
        raise ValueError(f"❌ URL for environment '{env}' not found in .env")

    os.environ["BASE_URL"] = base_url
    os.environ["SIGNUP_USERNAME"] = os.getenv("SIGNUP_USERNAME")
    os.environ["PASSWORD"] = os.getenv("PASSWORD")

    print(f"✅ BASE_URL: {base_url}")
    print(f"✅ Using user: {os.environ['SIGNUP_USERNAME']}")

# ✅ Create driver based on --mode
def create_driver(request, browser_name: str, build_name: str, test_name: str):
    mode = request.config.getoption("--mode")

    if mode == "lt":
        options = webdriver.ChromeOptions()
        options.platform_name = "Windows 11"
        options.browser_version = "latest"
        lt_options = {
            "build": build_name,
            "name": test_name,
            "selenium_version": "4.0.0",
            "network": True,
            "video": True,
            "console": True,
            "w3c": True
        }
        options.set_capability("LT:Options", lt_options)
        driver = webdriver.Remote(command_executor=LT_URL, options=options)
        return driver

    elif mode == "local":
        chrome_options = Options()
        chrome_options.add_argument("--start-maximized")
        driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)
        return driver

    else:
        raise ValueError(f"❌ Unknown mode: {mode}. Use 'local' or 'lt'.")

# ✅ Generic Setup Fixture
@pytest.fixture(params=["chrome"])
def setup(request):
    driver = create_driver(request, "chrome", "Test Login", "Login chrome")
    yield driver
    driver.quit()

# ✅ SP Dashboard Login Fixture
@pytest.fixture(params=["chrome"], scope="class")
def setup1(request):
    driver = create_driver(request, "chrome", "SP Dashboard", "SP Dashboard Visibility")
    driver.get(os.environ["BASE_URL"])
    driver.maximize_window()

    dv = DashboardVisibility(driver)
    dv.setUserName(os.environ["SIGNUP_USERNAME"])
    dv.setPassword(os.environ["PASSWORD"])
    dv.clicklogin()
    time.sleep(5)
    dv.clicksp()
    time.sleep(5)

    request.cls.driver = driver
    request.cls.dv = dv
    yield
    driver.quit()

# ✅ Brand Dashboard Login Fixture
@pytest.fixture(params=["chrome"], scope="class")
def setup2(request):
    driver = create_driver(request, "chrome", "Brand Dashboard", "Brand Dashboard Visibility")
    driver.get(os.environ["BASE_URL"])
    driver.maximize_window()

    dv = DashboardVisibility(driver)
    dv.setUserName(os.environ["SIGNUP_USERNAME"])
    dv.setPassword(os.environ["PASSWORD"])
    dv.clicklogin()
    time.sleep(5)
    dv.clickbrand()
    time.sleep(5)

    request.cls.driver = driver
    request.cls.dv = dv
    yield
    driver.quit()

# ✅ Logout Fixture
@pytest.fixture(params=["chrome"])
def setup3(request):
    driver = create_driver(request, "chrome", "Logout Test", "Logout")
    yield driver
    driver.quit()

# ✅ Service Request Creation Fixture
@pytest.fixture(params=["chrome"])
def setup4(request):
    driver = create_driver(request, "chrome", "Service Request Test", "Service Request Creation")
    yield driver
    driver.quit()
