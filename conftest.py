import os
import time
import pytest
from selenium import webdriver
from pageObject.DashboardPage import DashboardVisibility
from dotenv import load_dotenv

# ✅ Load environment variables from .env file
load_dotenv()

# ✅ LambdaTest Credentials
LT_USERNAME = os.getenv("LT_USERNAME")
LT_ACCESS_KEY = os.getenv("LT_ACCESS_KEY")
LT_URL = f"https://{LT_USERNAME}:{LT_ACCESS_KEY}@hub.lambdatest.com/wd/hub"

# ✅ Add --env argument to pytest command line options
def pytest_addoption(parser):
    parser.addoption(
        "--env", action="store", default="prod", help="Specify the environment (dev, demo, prod)"
    )

# ✅ Set environment variables based on the provided environment
@pytest.fixture(scope="session", autouse=True)
def set_environment(request):
    env = request.config.getoption("--env").lower()
    print(f"Setting up for {env.upper()} environment...")

    if env == "dev":
        os.environ["BASE_URL"] = "https://wify-tms-dev.wify.co.in"
    elif env == "demo":
        os.environ["BASE_URL"] = "https://wify-tms-demo.wify.co.in"
    elif env == "prod":
        os.environ["BASE_URL"] = "https://tms.wify.co.in"
    else:
        raise ValueError("Invalid environment! Use 'dev', 'demo', or 'prod'.")

    os.environ["SIGNUP_USERNAME"] = "<EMAIL>"
    os.environ["PASSWORD"] = "test"

    print(f"✅ Environment set to {env.upper()} with BASE_URL={os.environ['BASE_URL']}")

    # ✅ Define Cross-Browser Setup on LambdaTest
@pytest.fixture(params=["chrome"])
def setup(request):
        browser = request.param

        options = None
        if browser == "chrome":
            options = webdriver.ChromeOptions()
        # elif browser == "firefox":
        #     options = webdriver.FirefoxOptions()
        # elif browser == "edge":
        #     options = webdriver.EdgeOptions()

        options.platform_name = "Windows 11"
        options.browser_version = "latest"

        lt_options = {
            "build": "Test Login",
            "name": f"Login {browser}",
            "selenium_version": "4.0.0",
            "network": True,
            "video": True,
            "console": True,
            "w3c": True
        }

        options.set_capability("LT:Options", lt_options)

        # ✅ Start Remote WebDriver session on LambdaTest
        driver = webdriver.Remote(command_executor=LT_URL, options=options)
        yield driver
        driver.quit()  # ✅ Close session after test completion

@pytest.fixture(params=["chrome"], scope="class")
def setup1(request):
    browser = request.param

    if "BASE_URL" not in os.environ:
        os.environ["BASE_URL"] = "https://tms.wify.co.in"  # Default to prod

    if "SIGNUP_USERNAME" not in os.environ:
        os.environ["SIGNUP_USERNAME"] = "<EMAIL>"

    if "PASSWORD" not in os.environ:
        os.environ["PASSWORD"] = "test"

    print(f"Using BASE_URL: {os.environ['BASE_URL']}")
    print(f"Using SIGNUP_USERNAME: {os.environ['SIGNUP_USERNAME']}")

    options = None
    if browser == "chrome":
        options = webdriver.ChromeOptions()
    # elif browser == "firefox":
    #     options = webdriver.FirefoxOptions()
    # elif browser == "edge":
    #     options = webdriver.EdgeOptions()

    options.platform_name = "Windows 11"
    options.browser_version = "latest"

    lt_options = {
        "build": "Test SP Dashboard Section Visibility",
        "name": f"SP Dashboard Visibility - {browser}",
        "selenium_version": "4.0.0",
        "network": True,
        "video": True,
        "console": True,
        "w3c": True
    }
    options.set_capability("LT:Options", lt_options)

    driver = webdriver.Remote(command_executor=LT_URL, options=options)
    driver.get(os.environ["BASE_URL"])
    driver.maximize_window()

    dv = DashboardVisibility(driver)
    dv.setUserName(os.environ["SIGNUP_USERNAME"])
    dv.setPassword(os.environ["PASSWORD"])
    dv.clicklogin()
    time.sleep(5)
    dv.clicksp()
    time.sleep(5)

    request.cls.driver = driver
    request.cls.dv = dv
    yield
    driver.quit()

@pytest.fixture(params=["chrome"], scope="class")
def setup2(request):
    browser = request.param

    if "BASE_URL" not in os.environ:
        os.environ["BASE_URL"] = "https://tms.wify.co.in"  # Default to prod

    if "SIGNUP_USERNAME" not in os.environ:
        os.environ["SIGNUP_USERNAME"] = "<EMAIL>"

    if "PASSWORD" not in os.environ:
        os.environ["PASSWORD"] = "test"

    print(f"Using BASE_URL: {os.environ['BASE_URL']}")
    print(f"Using SIGNUP_USERNAME: {os.environ['SIGNUP_USERNAME']}")

    options = None
    if browser == "chrome":
        options = webdriver.ChromeOptions()

    options.platform_name = "Windows 11"
    options.browser_version = "latest"

    lt_options = {
        "build": "Test Brand Dashboard Section Visibility",
        "name": f"Brand Dashboard Visibility - {browser}",
        "selenium_version": "4.0.0",
        "network": True,
        "video": True,
        "console": True,
        "w3c": True
    }
    options.set_capability("LT:Options", lt_options)

    driver = webdriver.Remote(command_executor=LT_URL, options=options)
    driver.get(os.environ["BASE_URL"])
    driver.maximize_window()

    dv = DashboardVisibility(driver)
    dv.setUserName(os.environ["SIGNUP_USERNAME"])
    dv.setPassword(os.environ["PASSWORD"])
    dv.clicklogin()
    time.sleep(5)
    dv.clickbrand()
    time.sleep(5)

    request.cls.driver = driver
    request.cls.dv = dv
    yield
    driver.quit()

@pytest.fixture(params=["chrome"])
def setup3(request):
        browser = request.param

        options = None
        if browser == "chrome":
            options = webdriver.ChromeOptions()

        options.platform_name = "Windows 11"
        options.browser_version = "latest"

        lt_options = {
            "build": "Test Logout",
            "name": f"Logout {browser}",
            "selenium_version": "4.0.0",
            "network": True,
            "video": True,
            "console": True,
            "w3c": True
        }

        options.set_capability("LT:Options", lt_options)

        # ✅ Start Remote WebDriver session on LambdaTest
        driver = webdriver.Remote(command_executor=LT_URL, options=options)
        yield driver
        driver.quit()  # ✅ Close session after test completion


@pytest.fixture(params=["chrome"])
def setup4(request):
        browser = request.param

        options = None
        if browser == "chrome":
            options = webdriver.ChromeOptions()
        # elif browser == "firefox":
        #     options = webdriver.FirefoxOptions()
        # elif browser == "edge":
        #     options = webdriver.EdgeOptions()

        options.platform_name = "Windows 11"
        options.browser_version = "latest"

        lt_options = {
            "build": "Test Service Request Creation",
            "name": f"Test Service Request Creation {browser}",
            "selenium_version": "4.0.0",
            "network": True,
            "video": True,
            "console": True,
            "w3c": True
        }

        options.set_capability("LT:Options", lt_options)

        # ✅ Start Remote WebDriver session on LambdaTest
        driver = webdriver.Remote(command_executor=LT_URL, options=options)
        yield driver
        driver.quit()  # ✅ Close session after test completion