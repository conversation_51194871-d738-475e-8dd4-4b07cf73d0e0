from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains

class ServiceRequestCreationPage:
    def __init__(self, driver):
        self.driver = driver

    # Element locators
    task_based_link_text = "Task Based"
    add_button_xpath = "//button[@aria-label='add']"
    cust_mobile_id = "cust_mobile"
    cust_full_name_id = "cust_full_name"
    request_description_id = "request_description"
    request_priority_id = "request_priority"
    priority_high_xpath = "//div[@class='ant-select-item-option-content' and text()='High']"
    submit_button_xpath = "//button[@type='submit' and contains(., 'Submit')]"

    # Methods to interact with the service request page
    def navigate_to_task_based(self):
        task_based_link = WebDriverWait(self.driver, 10).until(
            EC.presence_of_element_located((By.LINK_TEXT, self.task_based_link_text))
        )
        task_based_link.click()

    def click_add_button(self):
        add_button = WebDriverWait(self.driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, self.add_button_xpath))
        )
        add_button.click()

    def enter_customer_mobile(self, mobile_number):
        mobile_input = WebDriverWait(self.driver, 10).until(
            EC.presence_of_element_located((By.ID, self.cust_mobile_id))
        )
        mobile_input.send_keys(mobile_number)

    def enter_customer_name(self, name):
        name_input = self.driver.find_element(By.ID, self.cust_full_name_id)
        name_input.clear()
        name_input.send_keys(name)

    def enter_request_description(self, description):
        description_field = WebDriverWait(self.driver, 10).until(
            EC.presence_of_element_located((By.ID, self.request_description_id))
        )
        description_field.send_keys(description)

    def select_priority_high(self):
        priority_dropdown = self.driver.find_element(By.ID, self.request_priority_id)
        self.driver.execute_script("arguments[0].scrollIntoView(true);", priority_dropdown)
        actions = ActionChains(self.driver)
        actions.move_to_element(priority_dropdown).click().perform()

        high_option = WebDriverWait(self.driver, 10).until(
            EC.visibility_of_element_located((By.XPATH, self.priority_high_xpath))
        )
        high_option.click()

    def click_submit_button(self):
        submit_button = WebDriverWait(self.driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, self.submit_button_xpath))
        )
        self.driver.execute_script("arguments[0].scrollIntoView(true);", submit_button)
        submit_button.click()
