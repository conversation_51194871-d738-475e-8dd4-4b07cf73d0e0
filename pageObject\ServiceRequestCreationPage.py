import time
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains

class ServiceRequestCreationPage:
    def __init__(self, driver):
        self.driver = driver

    # Element locators
    task_based_link_xpath = "/html/body/div/section/aside/div/div[2]/div[2]/div[1]/ul/li[8]/span/a"
    add_button_xpath = "//button[@aria-label='add']"
    cust_mobile_id = "cust_mobile"
    cust_full_name_id = "cust_full_name"
    request_description_id = "request_description"
    request_priority_id = "request_priority"
    priority_high_xpath = "//div[@class='ant-select-item-option-content' and text()='High']"
    submit_button_xpath = "//button[@type='submit' and contains(., 'Submit')]"

    def navigate_to_task_based(self):
        print("\n✅ Entered navigate_to_task_based")
        task_based_link = WebDriverWait(self.driver, 15).until(
            EC.element_to_be_clickable((By.XPATH, self.task_based_link_xpath))
        )
        self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", task_based_link)
        task_based_link.click()
        print("✅ Clicked Task Based link")

    def click_add_button(self):
        print("\n➡️ Clicking add button")
        add_button = WebDriverWait(self.driver, 15).until(
            EC.element_to_be_clickable((By.XPATH, self.add_button_xpath))
        )
        self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", add_button)
        add_button.click()
        print("✅ Clicked add button")
        time.sleep(1)  # Allow form to load

    def enter_customer_mobile(self, mobile_number):
        print("\n➡️ Entering mobile number")
        mobile_input = WebDriverWait(self.driver, 15).until(
            EC.visibility_of_element_located((By.ID, self.cust_mobile_id))
        )
        mobile_input.clear()
        mobile_input.send_keys(mobile_number)
        print("✅ Entered mobile number")
        time.sleep(2)

    def enter_customer_name(self, name):
        print("\n➡️ Entering customer name")
        name_input = WebDriverWait(self.driver, 15).until(
            EC.visibility_of_element_located((By.ID, self.cust_full_name_id))
        )
        name_input.clear()
        name_input.send_keys(name)
        print("✅ Entered customer name")
        time.sleep(2)

    def enter_request_description(self, description):
        print("\n➡️ Entering request description")
        description_field = WebDriverWait(self.driver, 15).until(
            EC.visibility_of_element_located((By.ID, self.request_description_id))
        )
        description_field.clear()
        description_field.send_keys(description)
        time.sleep(2)
        print("✅ Entered request description")

    def select_priority_high(self):
        priority_dropdown = self.driver.find_element(By.ID, self.request_priority_id)
        self.driver.execute_script("arguments[0].scrollIntoView(true);", priority_dropdown)
        actions = ActionChains(self.driver)
        actions.move_to_element(priority_dropdown).click().perform()

        high_option = WebDriverWait(self.driver, 10).until(
            EC.visibility_of_element_located((By.XPATH, self.priority_high_xpath))
        )
        high_option.click()
        time.sleep(2)
        print("✅ Selected High priority")

    def click_submit_button(self):
        print("\n➡️ Clicking submit button")

        # Use button instead of span inside it
        submit_button_xpath = "/html/body/div[2]/div/div[2]/div/div[2]/div[2]/div/div[2]/form/div[2]/button"

        submit_button = WebDriverWait(self.driver, 15).until(
            EC.element_to_be_clickable((By.XPATH, submit_button_xpath))
        )

        # Scroll and click
        self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", submit_button)
        time.sleep(5)  # optional wait after scroll
        submit_button.click()
        time.sleep(5)
        print("✅ Submitted the form")

