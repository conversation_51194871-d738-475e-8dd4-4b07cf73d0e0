name: SecondApproach

on:
  repository_dispatch:
    types: [trigger-repo-b-action-new]

jobs:
  run-tests:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      # - name: Install dependencies
      #   run: |
      #     python -m pip install --upgrade pip
      #     pip install selenium==4.* pytest pytest-html pytest-cov pytest-mock python-dotenv

      # - name: Run tests
      #   run: |
      #     pytest --maxfail=5 --disable-warnings --capture=no --html=report.html --self-contained-html
      #   # Capture the exit code of pytest
      #   continue-on-error: true # Allow the next step to run even if tests fail

      - name: Force failure (for testing)
        run: |
          echo "Forcing failure for testing..."
          exit 1  # This forces the job to fail, triggering a forced failure

      - name: Determine test result
        id: test_status
        run: |
          if [ $? -eq 0 ]; then
            echo "Tests passed"
            echo "::set-output name=status::success"  # If tests pass, set status to 'success'
          else
            echo "Tests failed"
            echo "::set-output name=status::failure"  # If tests fail, set status to 'failure'
          fi

      - name: Trigger Action C in Repo A on completion
        if: always() # Ensure this step runs even if tests fail
        run: |
          curl -X POST \
            -H "Authorization: token ****************************************" \
            -d '{"event_type": "repo-b-action-completed", "client_payload": {"status": "${{ steps.test_status.outputs.status }}", "run_url": "https://github.com/git-wify/wify_automation_testing/actions/runs/${{ github.run_id }}"}}' \
            https://api.github.com/repos/git-wify/tmsfordevops/dispatches
